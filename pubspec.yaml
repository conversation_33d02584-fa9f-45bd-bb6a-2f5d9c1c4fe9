name: cussme
description: "CussMe project."
publish_to: 'none'
version: 2.10.0+10

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0
  intl_utils: ^2.8.10
  freezed: ^3.0.4
  freezed_annotation: ^3.0.0
  go_router: ^14.8.1
  http: ^1.3.0
  font_awesome_flutter: ^10.8.0
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  json_annotation: ^4.9.0
  flutter_svg: ^2.0.10+1
  cupertino_icons: ^1.0.8
  supabase_flutter: ^2.3.3
  google_sign_in: ^6.2.1
  shared_preferences: ^2.5.3
  google_mobile_ads: ^5.3.1
  flutter_facebook_auth: ^7.1.1
  flutter_tts: ^4.2.2
  url_launcher: ^6.3.1
  in_app_purchase: ^3.2.0
  logger: ^2.0.2
  app_tracking_transparency: ^2.0.6+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  riverpod_generator: ^2.6.5
  build_runner: ^2.4.15
  custom_lint: ^0.7.5
  json_serializable: ^6.9.4
  go_router_builder: ^2.8.2
  build_verify: ^3.1.0
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

flutter_launcher_icons:
  image_path: "assets/icons/ic_launcher.png"
  android: true
  adaptive_icon_background: "#E61E25"
  adaptive_icon_foreground: "assets/icons/ic_launcher.png"
  minSdk: 23
  ios: true
  remove_alpha_ios: true
  background_color_ios: "#E61E25"

flutter_intl:
  enabled: true
  main_locale: en
  class_name: Str
  arb_dir: lib/localization
  output_dir: lib/localization/generated

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
  fonts:
    - family: Figtree
      fonts:
        - asset: assets/fonts/Figtree.ttf
        - asset: assets/fonts/Figtree-Italic.ttf
          style: italic
