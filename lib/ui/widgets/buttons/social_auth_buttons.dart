import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../localization/generated/l10n.dart';
import '../../styles/palette.dart';
import '../../styles/text_styles.dart';

class SocialAuthButtons extends StatelessWidget {
  final void Function() onGooglePressed;
  final void Function() onFacebookPressed;
  final bool isGoogleLoading;
  final bool isFacebookLoading;

  const SocialAuthButtons({
    super.key,
    required this.onGooglePressed,
    required this.onFacebookPressed,
    this.isGoogleLoading = false,
    this.isFacebookLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final str = Str.of(context);

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: isGoogleLoading ? null : onGooglePressed,
                style: OutlinedButton.styleFrom(
                  foregroundColor: Palette.primaryDark,
                  side: const BorderSide(color: Palette.outline),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100),
                  ),
                  padding: const EdgeInsets.fromLTRB(16, 10, 24, 10),
                ),
                icon: isGoogleLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Palette.primary,
                        ),
                      )
                    : SvgPicture.asset(
                        'assets/images/ic_google.svg',
                        height: 20,
                        width: 20,
                      ),
                label: Text(
                  str.googleButton,
                  style: TextStyles.labelLarge,
                ),
              ),
            ),
            // const SizedBox(width: 12),
            // Expanded(
            //   child: OutlinedButton.icon(
            //     onPressed: isFacebookLoading ? null : onFacebookPressed,
            //     style: OutlinedButton.styleFrom(
            //       foregroundColor: Palette.primaryDark,
            //       side: const BorderSide(color: Palette.outline),
            //       shape: RoundedRectangleBorder(
            //         borderRadius: BorderRadius.circular(100),
            //       ),
            //       padding: const EdgeInsets.fromLTRB(16, 10, 24, 10),
            //     ),
            //     icon: isFacebookLoading
            //         ? const SizedBox(
            //             height: 20,
            //             width: 20,
            //             child: CircularProgressIndicator(
            //               strokeWidth: 2,
            //               color: Palette.primary,
            //             ),
            //           )
            //         : SvgPicture.asset(
            //             'assets/images/ic_facebook.svg',
            //             height: 20,
            //             width: 20,
            //           ),
            //     label: Text(
            //       str.facebookButton,
            //       style: TextStyles.labelLarge,
            //     ),
            //   ),
            // ),
          ],
        ),
      ],
    );
  }
}
