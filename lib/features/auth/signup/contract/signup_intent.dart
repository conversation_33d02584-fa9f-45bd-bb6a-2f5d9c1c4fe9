import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup_intent.freezed.dart';

@freezed
sealed class SignUpIntent with _$SignUpIntent {
  const factory SignUpIntent.firstNameChanged(String firstName) =
      FirstNameChangedIntent;
  const factory SignUpIntent.lastNameChanged(String lastName) =
      LastNameChangedIntent;
  const factory SignUpIntent.emailChanged(String email) = EmailChangedIntent;
  const factory SignUpIntent.passwordChanged(String password) =
      PasswordChangedIntent;
  const factory SignUpIntent.confirmPasswordChanged(String confirmPassword) =
      ConfirmPasswordChangedIntent;
  const factory SignUpIntent.togglePasswordVisibility() =
      TogglePasswordVisibilityIntent;
  const factory SignUpIntent.toggleConfirmPasswordVisibility() =
      ToggleConfirmPasswordVisibilityIntent;
  const factory SignUpIntent.signUpWithEmail() = SignUpWithEmailIntent;
  const factory SignUpIntent.signUpWithGoogle() = SignUpWithGoogleIntent;
  const factory SignUpIntent.signUpWithFacebook() = SignUpWithFacebookIntent;
  const factory SignUpIntent.navigateToSignIn() = NavigateToSignInIntent;
  const factory SignUpIntent.continueWithoutSignUp() =
      ContinueWithoutSignUpIntent;
  const factory SignUpIntent.openPrivacyPolicy() = OpenPrivacyPolicyIntent;
  const factory SignUpIntent.openTermsAndConditions() =
      OpenTermsAndConditionsIntent;
  const factory SignUpIntent.openEula() = OpenEulaIntent;
}
