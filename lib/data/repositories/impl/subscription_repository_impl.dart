import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:logger/logger.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final InAppPurchase _inAppPurchase;
  final SupabaseClient _supabaseClient;
  final _purchaseResultStreamController =
      StreamController<PurchaseResult>.broadcast();

  StreamSubscription<List<PurchaseDetails>>? _purchaseStreamSubscription;
  bool isRestore = false;
  bool _isDisposed = false;

  SubscriptionRepositoryImpl(this._inAppPurchase, this._supabaseClient) {
    _purchaseStreamSubscription =
        _inAppPurchase.purchaseStream.listen(_handlePurchaseUpdates);
  }

  @override
  void dispose() {
    if (_isDisposed) return;
    _isDisposed = true;
    _purchaseStreamSubscription?.cancel();
    _purchaseResultStreamController.close();
  }

  @override
  Stream<PurchaseResult> get purchaseResultStream =>
      _purchaseResultStreamController.stream;

  /// Filters purchase details to get only the most recent valid purchase for iOS
  /// to avoid processing expired subscriptions during new purchase flows
  List<PurchaseDetails> _filterValidPurchases(
      List<PurchaseDetails> purchaseDetailsList) {
    if (isRestore || !Platform.isIOS || purchaseDetailsList.length <= 1) {
      return purchaseDetailsList;
    }

    // Filter to only purchased or restored status
    final validPurchases = purchaseDetailsList
        .where((p) =>
            p.status == PurchaseStatus.purchased ||
            p.status == PurchaseStatus.restored)
        .toList();

    if (validPurchases.isEmpty) {
      return purchaseDetailsList; // Return original if no valid purchases
    }

    if (validPurchases.length == 1) {
      return validPurchases;
    }

    // Find the most recent purchase by transaction date
    final mostRecentPurchase = validPurchases.reduce((a, b) {
      final aDate = a.transactionDate;
      final bDate = b.transactionDate;
      if (aDate != null && bDate != null) {
        return aDate.compareTo(bDate) > 0 ? a : b;
      }
      return b; // Default to the later one in the list
    });

    return [mostRecentPurchase];
  }

  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    if (_isDisposed) return;

    if (!isRestore) {
      final lastPurchase = purchaseDetailsList.last;
      if (lastPurchase.status == PurchaseStatus.error) {
        _purchaseResultStreamController.add(
          PurchaseResult.error(message: lastPurchase.error!.message),
        );
        return;
      } else if (lastPurchase.status == PurchaseStatus.canceled) {
        _purchaseResultStreamController.add(const PurchaseResult.cancelled());
        return;
      }
    }

    // Filter purchases to get only valid ones (most recent for iOS new purchases)
    final filteredPurchases = _filterValidPurchases(purchaseDetailsList);

    final List<SubscriptionEntity> purchases = [];
    for (final purchaseDetails in filteredPurchases) {
      // For new purchases (not restore), only process purchases that are purchased or restored
      // and skip any that might be expired or invalid
      if (!isRestore &&
          purchaseDetails.status != PurchaseStatus.purchased &&
          purchaseDetails.status != PurchaseStatus.restored) {
        continue;
      }

      String purchaseToken;

      if (Platform.isIOS) {
        purchaseToken = purchaseDetails.verificationData.localVerificationData;
      } else {
        purchaseToken = purchaseDetails.verificationData.serverVerificationData;
      }

      purchases.add(
        SubscriptionEntity(
          productId: purchaseDetails.productID,
          platform: getPlatform(),
          purchaseToken: purchaseToken,
          purchaseId: purchaseDetails.purchaseID,
        ),
      );

      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }

    // Only send success if we have valid purchases to process
    if (purchases.isNotEmpty) {
      _purchaseResultStreamController.add(
        PurchaseResult.success(purchases: purchases),
      );
    }
  }

  @override
  Future<ProductDetails> getProductDetail(String productId) async {
    try {
      final ProductDetailsResponse response =
          await _inAppPurchase.queryProductDetails({productId});

      if (response.error != null || response.productDetails.isEmpty) {
        throw ExceptionHandler.handleError(response.error!.message);
      }

      return response.productDetails.first;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> purchaseProduct(PurchaseParam purchaseParam) async {
    isRestore = false;

    try {
      final bool success =
          await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);

      if (!success) {
        throw ExceptionHandler.handleError(
            Str.current.failedToInitiatePurchase);
      }
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> restorePurchases(String userId) async {
    isRestore = true;
    try {
      await _inAppPurchase.restorePurchases(applicationUserName: userId);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> isInAppPurchaseAvailable() async {
    return await _inAppPurchase.isAvailable();
  }

  @override
  Future<SubscriptionVerificationResponse?> verifyAndSubmit(
      SubscriptionVerificationRequest request) async {
    try {
      final response = await http.post(
        Uri.parse(verifyAndSubmitPurchaseFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization':
              _supabaseClient.auth.currentSession!.accessToken.bearer,
        },
        body: jsonEncode(request.toJson()),
      );

      return SubscriptionVerificationResponse.fromJson(
          jsonDecode(response.body));
    } catch (_) {
      return null;
    }
  }
}
