import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_purchase/in_app_purchase.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final InAppPurchase _inAppPurchase;
  final SupabaseClient _supabaseClient;
  final _purchaseResultStreamController =
      StreamController<PurchaseResult>.broadcast();

  StreamSubscription<List<PurchaseDetails>>? _purchaseStreamSubscription;
  bool isRestore = false;
  bool _isDisposed = false;

  // Track the product ID we're currently trying to purchase
  String? _currentPurchaseProductId;

  SubscriptionRepositoryImpl(this._inAppPurchase, this._supabaseClient) {
    _purchaseStreamSubscription =
        _inAppPurchase.purchaseStream.listen(_handlePurchaseUpdates);
  }

  @override
  void dispose() {
    if (_isDisposed) return;
    _isDisposed = true;
    _purchaseStreamSubscription?.cancel();
    _purchaseResultStreamController.close();
  }

  @override
  Stream<PurchaseResult> get purchaseResultStream =>
      _purchaseResultStreamController.stream;

  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    if (_isDisposed) return;

    // Debug logging for iOS to understand what's happening
    if (Platform.isIOS) {
      print('=== iOS Purchase Update Debug ===');
      print('isRestore: $isRestore');
      print('Purchase count: ${purchaseDetailsList.length}');
      for (int i = 0; i < purchaseDetailsList.length; i++) {
        final purchase = purchaseDetailsList[i];
        print('Purchase $i:');
        print('  - Product ID: ${purchase.productID}');
        print('  - Status: ${purchase.status}');
        print('  - Transaction Date: ${purchase.transactionDate}');
        print('  - Purchase ID: ${purchase.purchaseID}');
      }
      print('================================');
    }

    if (!isRestore) {
      final lastPurchase = purchaseDetailsList.last;
      if (lastPurchase.status == PurchaseStatus.error) {
        _purchaseResultStreamController.add(
          PurchaseResult.error(message: lastPurchase.error!.message),
        );
        return;
      } else if (lastPurchase.status == PurchaseStatus.canceled) {
        _purchaseResultStreamController.add(const PurchaseResult.cancelled());
        return;
      }
    }

    final List<SubscriptionEntity> purchases = [];
    for (final purchaseDetails in purchaseDetailsList) {
      // For iOS new purchases, skip restored purchases that are not actual new purchases
      if (!isRestore && Platform.isIOS) {
        // If this is a restored purchase and we're trying to make a new purchase, skip it
        if (purchaseDetails.status == PurchaseStatus.restored &&
            _currentPurchaseProductId != null) {
          print(
              'Skipping restored purchase for ${purchaseDetails.productID} - looking for new purchase of $_currentPurchaseProductId');
          continue;
        }

        // Only process purchases that match the current purchase attempt
        if (_currentPurchaseProductId != null &&
            purchaseDetails.productID != _currentPurchaseProductId) {
          print(
              'Skipping purchase for ${purchaseDetails.productID} - not the current purchase (${_currentPurchaseProductId})');
          continue;
        }
      }

      String purchaseToken;

      if (Platform.isIOS) {
        purchaseToken = purchaseDetails.verificationData.localVerificationData;
      } else {
        purchaseToken = purchaseDetails.verificationData.serverVerificationData;
      }

      purchases.add(
        SubscriptionEntity(
          productId: purchaseDetails.productID,
          platform: getPlatform(),
          purchaseToken: purchaseToken,
          purchaseId: purchaseDetails.purchaseID,
        ),
      );

      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }

    if (Platform.isIOS) {
      print('Sending ${purchases.length} purchases to server for verification');
    }

    // Clear the current purchase product ID after processing
    if (!isRestore && _currentPurchaseProductId != null) {
      _currentPurchaseProductId = null;
    }

    _purchaseResultStreamController.add(
      PurchaseResult.success(purchases: purchases),
    );
  }

  @override
  Future<ProductDetails> getProductDetail(String productId) async {
    try {
      final ProductDetailsResponse response =
          await _inAppPurchase.queryProductDetails({productId});

      if (response.error != null || response.productDetails.isEmpty) {
        throw ExceptionHandler.handleError(response.error!.message);
      }

      return response.productDetails.first;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> purchaseProduct(PurchaseParam purchaseParam) async {
    isRestore = false;

    // Track the product ID we're trying to purchase
    _currentPurchaseProductId = purchaseParam.productDetails.id;

    try {
      // Don't sync or refresh for iOS as it causes restored purchases to be returned
      // instead of processing new purchases

      final bool success =
          await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);

      if (!success) {
        _currentPurchaseProductId = null; // Clear on failure
        throw ExceptionHandler.handleError(
            Str.current.failedToInitiatePurchase);
      }
    } catch (e) {
      _currentPurchaseProductId = null; // Clear on error
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> restorePurchases(String userId) async {
    isRestore = true;
    try {
      await _inAppPurchase.restorePurchases(applicationUserName: userId);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> isInAppPurchaseAvailable() async {
    return await _inAppPurchase.isAvailable();
  }

  @override
  Future<SubscriptionVerificationResponse?> verifyAndSubmit(
      SubscriptionVerificationRequest request) async {
    try {
      final response = await http.post(
        Uri.parse(verifyAndSubmitPurchaseFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization':
              _supabaseClient.auth.currentSession!.accessToken.bearer,
        },
        body: jsonEncode(request.toJson()),
      );

      return SubscriptionVerificationResponse.fromJson(
          jsonDecode(response.body));
    } catch (_) {
      return null;
    }
  }
}
